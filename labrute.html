<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaBrute Reborn - Combat Arena</title>
    <script type="importmap">
      {
        "imports": {
          "phaser": "https://esm.sh/phaser@3.70.0"
        }
      }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(180deg, #2a1810 0%, #1a0f08 100%);
            font-family: 'Arial Black', 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        #game-wrapper {
            width: 100%;
            height: 100%;
            max-width: 1024px;
            max-height: 768px;
            position: relative;
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.8);
        }
        
        #phaser-game-container {
            width: 100%;
            height: 100%;
            background: #000;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff6b35;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* Hide loading when game starts */
        .game-started .loading {
            display: none;
        }
    </style>
</head>
<body>
    <div id="game-wrapper">
        <div id="phaser-game-container"></div>
        <div class="loading">Chargement du combat...</div>
    </div>
    
    <script type="module">
        // Import the game
        import('./src/main.js').then(() => {
            // Remove loading message when game starts
            setTimeout(() => {
                document.body.classList.add('game-started');
            }, 1000);
        }).catch(error => {
            console.error('Erreur de chargement:', error);
            document.querySelector('.loading').textContent = 'Erreur de chargement. Rafraîchissez la page.';
        });
        
        // Prevent right-click context menu
        document.addEventListener('contextmenu', e => e.preventDefault());
        
        // Log game info
        console.log('%cLaBrute Reborn', 'color: #ff6b35; font-size: 24px; font-weight: bold;');
        console.log('%cCombat Arena - Version Optimisée', 'color: #ffaa00; font-size: 16px;');
    </script>
</body>
</html>