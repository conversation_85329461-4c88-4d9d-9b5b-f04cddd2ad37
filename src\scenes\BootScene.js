import Phaser from 'phaser';

export class BootScene extends Phaser.Scene {
  constructor() {
    super({ key: 'Boot' });
  }

  preload() {
    // Load all LaBrute assets - using remote URLs for now
    this.load.image('background', 'https://play.rosebud.ai/assets/background.png?Tzv3');
    this.load.image('bruteMale', 'https://play.rosebud.ai/assets/bruteMale.png?YX5C');
    this.load.image('bruteFemale', 'https://play.rosebud.ai/assets/bruteFemale.png?5zm3');
    this.load.image('swordWeapon', 'https://play.rosebud.ai/assets/swordWeapon.png?Qlgm');
    this.load.image('hammerWeapon', 'https://play.rosebud.ai/assets/hammerWeapon.png?s24L');
    this.load.image('fight1', 'https://play.rosebud.ai/assets/fight1.png?yy2S');
    this.load.image('fight2', 'https://play.rosebud.ai/assets/fight2.png?WJie');
    
    // Skip warrior spritesheet for now to test if game loads
    // this.load.spritesheet('warrior', 'assets/animations/Free-Warrior-Pixel-Art-Sprite-Sheets2.jpg', {
    //   frameWidth: 69,
    //   frameHeight: 44
    // });
    
    // Loading progress bar
    const progressBg = this.add.rectangle(512, 384, 400, 20, 0x333333);
    const progressBar = this.add.rectangle(512, 384, 0, 16, 0xff6b35);
    
    this.load.on('progress', (value) => {
      progressBar.width = 400 * value;
    });
    
    this.load.on('complete', () => {
      this.time.delayedCall(500, () => this.scene.start('Fight'));
    });
  }
}